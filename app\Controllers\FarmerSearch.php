<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\LivestockFarmDataModel;
use App\Models\CropsFarmMarketingDataModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\AdxCountryModel;
use App\Models\CropsModel;
use App\Models\LivestockModel;

class FarmerSearch extends BaseController
{
    protected $farmerModel;
    protected $cropsFarmBlockModel;
    protected $livestockFarmBlockModel;
    protected $cropsFarmCropsDataModel;
    protected $livestockFarmDataModel;
    protected $cropsFarmMarketingDataModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $countryModel;
    protected $cropsModel;
    protected $livestockModel;

    public function __construct()
    {
        helper(['form', 'url']);
        
        // Initialize models
        $this->farmerModel = new FarmerInformationModel();
        $this->cropsFarmBlockModel = new CropsFarmBlockModel();
        $this->livestockFarmBlockModel = new LivestockFarmBlockModel();
        $this->cropsFarmCropsDataModel = new CropsFarmCropsDataModel();
        $this->livestockFarmDataModel = new LivestockFarmDataModel();
        $this->cropsFarmMarketingDataModel = new CropsFarmMarketingDataModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->countryModel = new AdxCountryModel();
        $this->cropsModel = new CropsModel();
        $this->livestockModel = new LivestockModel();
    }

    /**
     * Display farmer search form
     * GET /farmer-search
     */
    public function index()
    {
        $data = [
            'title' => 'Farmer Search',
            'menu' => 'farmer_search',
            'page_header' => 'Search for Farmer'
        ];

        return view('home_farmer_search/home_farmer_search_index', $data);
    }

    /**
     * Process farmer search
     * POST /farmer-search
     */
    public function search()
    {
        try {
            $farmerCode = $this->request->getPost('farmer_code');
            
            if (empty($farmerCode)) {
                return redirect()->back()
                    ->with('error', 'Please enter a farmer code to search.');
            }

            // Search for farmer by farmer code
            $farmer = $this->farmerModel
                ->select('farmer_information.*,
                         adx_province.name as province_name,
                         adx_district.name as district_name,
                         adx_llg.name as llg_name,
                         adx_ward.name as ward_name,
                         adx_country.name as country_name')
                ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
                ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
                ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
                ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
                ->join('adx_country', 'adx_country.id = farmer_information.country_id', 'left')
                ->where('farmer_information.farmer_code', $farmerCode)
                ->where('farmer_information.status', 'active')
                ->first();

            if (!$farmer) {
                return redirect()->back()
                    ->with('error', 'No farmer found with the provided farmer code: ' . $farmerCode);
            }

            $data = [
                'title' => 'Farmer Search Results',
                'menu' => 'farmer_search',
                'page_header' => 'Search Results',
                'farmer' => $farmer,
                'farmer_code' => $farmerCode
            ];

            return view('home_farmer_search/home_farmer_search_results', $data);

        } catch (\Exception $e) {
            log_message('error', '[Farmer Search] ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while searching. Please try again.');
        }
    }

    /**
     * Display farmer profile
     * GET /farmer-search/profile/{farmer_id}
     */
    public function profile($farmerId)
    {
        try {
            // Get farmer with location details
            $farmer = $this->getFarmerWithLocationDetails($farmerId);
            
            if (!$farmer) {
                return redirect()->to('farmer-search')
                    ->with('error', 'Farmer not found.');
            }

            // Get farmer's crop farm blocks
            $cropBlocks = $this->getCropFarmBlocks($farmerId);

            // Get farmer's livestock farm blocks
            $livestockBlocks = $this->getLivestockFarmBlocks($farmerId);

            // Get farmer's marketing data
            $marketingData = $this->getMarketingData($farmerId);

            // Get total count of marketing records
            $totalMarketingCount = $this->cropsFarmMarketingDataModel
                ->where('farmer_id', $farmerId)
                ->where('status', 'active')
                ->countAllResults();

            // Calculate total hectares
            $totalHectares = 0;
            foreach ($cropBlocks as $block) {
                $totalHectares += floatval($block['total_hectares'] ?? 0);
            }

            // Prepare GPS locations for mapping
            $gpsLocations = [];
            foreach ($cropBlocks as $block) {
                if (!empty($block['lat']) && !empty($block['lon'])) {
                    $gpsLocations[] = [
                        'type' => 'crop',
                        'block_code' => $block['block_code'],
                        'crop_name' => $block['crop_name'],
                        'lat' => floatval($block['lat']),
                        'lon' => floatval($block['lon']),
                        'site' => $block['block_site'],
                        'village' => $block['village'],
                        'hectares' => floatval($block['total_hectares'] ?? 0)
                    ];
                }
            }

            foreach ($livestockBlocks as $block) {
                if (!empty($block['lat']) && !empty($block['lon'])) {
                    $gpsLocations[] = [
                        'type' => 'livestock',
                        'block_code' => $block['block_code'],
                        'lat' => floatval($block['lat']),
                        'lon' => floatval($block['lon']),
                        'site' => $block['block_site'],
                        'village' => $block['village']
                    ];
                }
            }

            $data = [
                'title' => 'Farmer Profile - ' . $farmer['given_name'] . ' ' . $farmer['surname'],
                'menu' => 'farmer_search',
                'page_header' => 'Farmer Profile',
                'farmer' => $farmer,
                'crop_blocks' => $cropBlocks,
                'livestock_blocks' => $livestockBlocks,
                'marketing_data' => $marketingData,
                'total_marketing_count' => $totalMarketingCount,
                'total_hectares' => $totalHectares,
                'gps_locations' => $gpsLocations
            ];

            return view('home_farmer_search/home_farmer_search_profile', $data);

        } catch (\Exception $e) {
            log_message('error', '[Farmer Profile] ' . $e->getMessage());
            return redirect()->to('farmer-search')
                ->with('error', 'An error occurred while loading farmer profile.');
        }
    }

    /**
     * Get farmer with detailed location information
     */
    private function getFarmerWithLocationDetails($farmerId)
    {
        return $this->farmerModel
            ->select('farmer_information.*,
                     adx_province.name as province_name,
                     adx_district.name as district_name,
                     adx_llg.name as llg_name,
                     adx_ward.name as ward_name,
                     adx_country.name as country_name')
            ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
            ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
            ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
            ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
            ->join('adx_country', 'adx_country.id = farmer_information.country_id', 'left')
            ->where('farmer_information.id', $farmerId)
            ->where('farmer_information.status', 'active')
            ->first();
    }

    /**
     * Get farmer's crop farm blocks with crop details and hectares
     */
    private function getCropFarmBlocks($farmerId)
    {
        return $this->cropsFarmBlockModel
            ->select('crops_farm_blocks.*,
                     adx_crops.crop_name,
                     adx_crops.crop_color_code,
                     (SELECT COUNT(*) FROM crops_farm_crops_data
                      WHERE crops_farm_crops_data.block_id = crops_farm_blocks.id
                      AND crops_farm_crops_data.status = "active") as crops_data_count,
                     (SELECT SUM(hectares) FROM crops_farm_crops_data
                      WHERE crops_farm_crops_data.block_id = crops_farm_blocks.id
                      AND crops_farm_crops_data.status = "active"
                      AND crops_farm_crops_data.action_type = "add") as total_hectares')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->where('crops_farm_blocks.farmer_id', $farmerId)
            ->where('crops_farm_blocks.status', 'active')
            ->findAll();
    }

    /**
     * Get farmer's livestock farm blocks with livestock details
     */
    private function getLivestockFarmBlocks($farmerId)
    {
        return $this->livestockFarmBlockModel
            ->select('livestock_farm_blocks.*,
                     (SELECT COUNT(*) FROM livestock_farm_data 
                      WHERE livestock_farm_data.block_id = livestock_farm_blocks.id 
                      AND livestock_farm_data.status = "active") as livestock_data_count')
            ->where('livestock_farm_blocks.farmer_id', $farmerId)
            ->where('livestock_farm_blocks.status', 'active')
            ->findAll();
    }

    /**
     * Get farmer's marketing data
     */
    private function getMarketingData($farmerId)
    {
        return $this->cropsFarmMarketingDataModel
            ->select('crops_farm_marketing_data.*,
                     adx_crops.crop_name,
                     crop_buyers.name as buyer_name')
            ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id', 'left')
            ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
            ->where('crops_farm_marketing_data.farmer_id', $farmerId)
            ->where('crops_farm_marketing_data.status', 'active')
            ->orderBy('crops_farm_marketing_data.market_date', 'DESC')
            ->limit(5)
            ->findAll();
    }
}
