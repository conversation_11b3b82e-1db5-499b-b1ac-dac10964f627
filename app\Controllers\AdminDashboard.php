<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UsersModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\PermissionsSetsModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\AdxDistrictModel;

class AdminDashboard extends BaseController
{
    protected $usersModel;
    protected $farmerModel;
    protected $cropBlockModel;
    protected $livestockBlockModel;
    protected $permissionsSetsModel;
    protected $permissionsUserDistrictsModel;
    protected $districtModel;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropBlockModel = new CropsFarmBlockModel();
        $this->livestockBlockModel = new LivestockFarmBlockModel();
        $this->permissionsSetsModel = new PermissionsSetsModel();
        $this->permissionsUserDistrictsModel = new PermissionsUserDistrictsModel();
        $this->districtModel = new AdxDistrictModel();
        helper(['form', 'url', 'info']);
    }

    /**
     * Admin Dashboard - Using hardcoded dummy data
     */
    public function index()
    {
        // Hardcoded dummy data for admin dashboard
        $stats = [
            'total_users' => 45,
            'active_users' => 38,
            'admin_users' => 3,
            'supervisor_users' => 5,
            'field_users' => 37,
            'total_farmers' => 1250,
            'active_farmers' => 1180,
            'total_crop_blocks' => 890,
            'total_livestock_blocks' => 340
        ];

        // Dummy crops data following adx_crops schema
        $cropsData = [
            [
                'id' => 1,
                'crop_name' => 'Sweet Potato',
                'crop_icon' => 'fas fa-seedling',
                'crop_color_code' => '#FF6B35',
                'remarks' => 'High yield variety suitable for PNG climate',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'total_blocks' => 245,
                'total_hectares' => 1250.75,
                'total_plants' => 125000,
                'avg_yield_per_hectare' => 15.5
            ],
            [
                'id' => 2,
                'crop_name' => 'Banana',
                'crop_icon' => 'fas fa-leaf',
                'crop_color_code' => '#FFD23F',
                'remarks' => 'Cavendish variety for export market',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-8 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'total_blocks' => 180,
                'total_hectares' => 890.25,
                'total_plants' => 89000,
                'avg_yield_per_hectare' => 25.8
            ],
            [
                'id' => 3,
                'crop_name' => 'Cocoa',
                'crop_icon' => 'fas fa-coffee',
                'crop_color_code' => '#8B4513',
                'remarks' => 'Premium quality cocoa beans',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 year')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'total_blocks' => 156,
                'total_hectares' => 780.50,
                'total_plants' => 78000,
                'avg_yield_per_hectare' => 1.2
            ],
            [
                'id' => 4,
                'crop_name' => 'Coffee',
                'crop_icon' => 'fas fa-coffee',
                'crop_color_code' => '#6F4E37',
                'remarks' => 'Arabica variety for highland regions',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-10 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'total_blocks' => 134,
                'total_hectares' => 670.25,
                'total_plants' => 67000,
                'avg_yield_per_hectare' => 0.8
            ],
            [
                'id' => 5,
                'crop_name' => 'Coconut',
                'crop_icon' => 'fas fa-tree',
                'crop_color_code' => '#8FBC8F',
                'remarks' => 'Dwarf variety for copra production',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 years')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'total_blocks' => 98,
                'total_hectares' => 490.75,
                'total_plants' => 49000,
                'avg_yield_per_hectare' => 12.5
            ],
            [
                'id' => 6,
                'crop_name' => 'Taro',
                'crop_icon' => 'fas fa-leaf',
                'crop_color_code' => '#32CD32',
                'remarks' => 'Traditional staple crop',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-4 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'total_blocks' => 77,
                'total_hectares' => 385.50,
                'total_plants' => 38500,
                'avg_yield_per_hectare' => 18.2
            ]
        ];

        // Dummy livestock data following adx_livestock schema
        $livestockData = [
            [
                'id' => 1,
                'name' => 'Chicken',
                'icon' => 'fas fa-egg',
                'color_code' => '#FFD700',
                'remarks' => 'Broiler and layer chickens',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'status' => 1,
                'total_blocks' => 156,
                'total_male' => 2340,
                'total_female' => 4680,
                'avg_cost_per_head' => 25.50,
                'avg_market_price' => 45.75
            ],
            [
                'id' => 2,
                'name' => 'Pig',
                'icon' => 'fas fa-piggy-bank',
                'color_code' => '#FFC0CB',
                'remarks' => 'Local and crossbred varieties',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-7 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'status' => 1,
                'total_blocks' => 89,
                'total_male' => 445,
                'total_female' => 890,
                'avg_cost_per_head' => 150.00,
                'avg_market_price' => 280.50
            ],
            [
                'id' => 3,
                'name' => 'Goat',
                'icon' => 'fas fa-horse-head',
                'color_code' => '#DEB887',
                'remarks' => 'Boer and local breeds',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'status' => 1,
                'total_blocks' => 67,
                'total_male' => 201,
                'total_female' => 402,
                'avg_cost_per_head' => 120.00,
                'avg_market_price' => 220.25
            ],
            [
                'id' => 4,
                'name' => 'Duck',
                'icon' => 'fas fa-dove',
                'color_code' => '#87CEEB',
                'remarks' => 'Muscovy and Pekin ducks',
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 months')),
                'updated_by' => 1,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'status' => 1,
                'total_blocks' => 28,
                'total_male' => 168,
                'total_female' => 336,
                'avg_cost_per_head' => 18.00,
                'avg_market_price' => 35.50
            ]
        ];

        // Crop production trends (monthly data for the last 12 months)
        $cropProductionTrends = [
            ['month' => 'Jan 2024', 'sweet_potato' => 1250, 'banana' => 890, 'cocoa' => 780, 'coffee' => 670, 'coconut' => 490, 'taro' => 385],
            ['month' => 'Feb 2024', 'sweet_potato' => 1280, 'banana' => 920, 'cocoa' => 795, 'coffee' => 685, 'coconut' => 505, 'taro' => 400],
            ['month' => 'Mar 2024', 'sweet_potato' => 1310, 'banana' => 945, 'cocoa' => 810, 'coffee' => 700, 'coconut' => 520, 'taro' => 415],
            ['month' => 'Apr 2024', 'sweet_potato' => 1340, 'banana' => 970, 'cocoa' => 825, 'coffee' => 715, 'coconut' => 535, 'taro' => 430],
            ['month' => 'May 2024', 'sweet_potato' => 1375, 'banana' => 995, 'cocoa' => 840, 'coffee' => 730, 'coconut' => 550, 'taro' => 445],
            ['month' => 'Jun 2024', 'sweet_potato' => 1400, 'banana' => 1020, 'cocoa' => 855, 'coffee' => 745, 'coconut' => 565, 'taro' => 460],
            ['month' => 'Jul 2024', 'sweet_potato' => 1425, 'banana' => 1045, 'cocoa' => 870, 'coffee' => 760, 'coconut' => 580, 'taro' => 475],
            ['month' => 'Aug 2024', 'sweet_potato' => 1450, 'banana' => 1070, 'cocoa' => 885, 'coffee' => 775, 'coconut' => 595, 'taro' => 490],
            ['month' => 'Sep 2024', 'sweet_potato' => 1475, 'banana' => 1095, 'cocoa' => 900, 'coffee' => 790, 'coconut' => 610, 'taro' => 505],
            ['month' => 'Oct 2024', 'sweet_potato' => 1500, 'banana' => 1120, 'cocoa' => 915, 'coffee' => 805, 'coconut' => 625, 'taro' => 520],
            ['month' => 'Nov 2024', 'sweet_potato' => 1525, 'banana' => 1145, 'cocoa' => 930, 'coffee' => 820, 'coconut' => 640, 'taro' => 535],
            ['month' => 'Dec 2024', 'sweet_potato' => 1550, 'banana' => 1170, 'cocoa' => 945, 'coffee' => 835, 'coconut' => 655, 'taro' => 550]
        ];

        // Livestock population trends (monthly data for the last 12 months)
        $livestockPopulationTrends = [
            ['month' => 'Jan 2024', 'chicken' => 6800, 'pig' => 1200, 'goat' => 580, 'duck' => 480],
            ['month' => 'Feb 2024', 'chicken' => 6950, 'pig' => 1235, 'goat' => 595, 'duck' => 495],
            ['month' => 'Mar 2024', 'chicken' => 7100, 'pig' => 1270, 'goat' => 610, 'duck' => 510],
            ['month' => 'Apr 2024', 'chicken' => 7250, 'pig' => 1305, 'goat' => 625, 'duck' => 525],
            ['month' => 'May 2024', 'chicken' => 7400, 'pig' => 1340, 'goat' => 640, 'duck' => 540],
            ['month' => 'Jun 2024', 'chicken' => 7550, 'pig' => 1375, 'goat' => 655, 'duck' => 555],
            ['month' => 'Jul 2024', 'chicken' => 7700, 'pig' => 1410, 'goat' => 670, 'duck' => 570],
            ['month' => 'Aug 2024', 'chicken' => 7850, 'pig' => 1445, 'goat' => 685, 'duck' => 585],
            ['month' => 'Sep 2024', 'chicken' => 8000, 'pig' => 1480, 'goat' => 700, 'duck' => 600],
            ['month' => 'Oct 2024', 'chicken' => 8150, 'pig' => 1515, 'goat' => 715, 'duck' => 615],
            ['month' => 'Nov 2024', 'chicken' => 8300, 'pig' => 1550, 'goat' => 730, 'duck' => 630],
            ['month' => 'Dec 2024', 'chicken' => 8450, 'pig' => 1585, 'goat' => 745, 'duck' => 645]
        ];

        // Dummy recent users data following users table schema
        $recentUsers = [
            [
                'id' => 15,
                'sys_no' => 202501,
                'org_id' => 1,
                'name' => 'John Smith',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Field Officer',
                'id_photo' => 'public/uploads/users/john_smith.jpg',
                'phone' => '+675 7123 4567',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'status_by' => 1,
                'status_remarks' => 'Active user',
                'created_by' => 'admin',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'id' => 16,
                'sys_no' => 202502,
                'org_id' => 1,
                'name' => 'Sarah Johnson',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 1,
                'position' => 'Regional Supervisor',
                'id_photo' => 'public/uploads/users/sarah_johnson.jpg',
                'phone' => '+675 7234 5678',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'status_by' => 1,
                'status_remarks' => 'Active supervisor',
                'created_by' => 'admin',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
            ],
            [
                'id' => 17,
                'sys_no' => 202503,
                'org_id' => 1,
                'name' => 'Michael Brown',
                'role' => 'user',
                'is_admin' => 1,
                'is_supervisor' => 0,
                'position' => 'System Administrator',
                'id_photo' => 'public/uploads/users/michael_brown.jpg',
                'phone' => '+675 7345 6789',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'status_by' => 1,
                'status_remarks' => 'Admin user',
                'created_by' => 'system',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
            ],
            [
                'id' => 18,
                'sys_no' => 202504,
                'org_id' => 1,
                'name' => 'Emily Davis',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Data Entry Clerk',
                'id_photo' => 'public/uploads/users/emily_davis.jpg',
                'phone' => '+675 7456 7890',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                'status_by' => 1,
                'status_remarks' => 'Active user',
                'created_by' => 'admin',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-10 days'))
            ],
            [
                'id' => 19,
                'sys_no' => 202505,
                'org_id' => 1,
                'name' => 'David Wilson',
                'role' => 'guest',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Temporary Access',
                'id_photo' => 'public/uploads/users/default.jpg',
                'phone' => '+675 7567 8901',
                'email' => '<EMAIL>',
                'status' => 0,
                'status_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'status_by' => 1,
                'status_remarks' => 'Inactive guest account',
                'created_by' => 'admin',
                'updated_by' => 'admin',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
            ]
        ];

        // Dummy user distribution by role
        $usersByRole = [
            ['role' => 'user', 'count' => 37],
            ['role' => 'guest', 'count' => 8]
        ];

        // Dummy farmers data following farmer_information schema
        $recentFarmers = [
            [
                'id' => 1,
                'org_id' => 1,
                'farmer_code' => 'F001',
                'given_name' => 'Peter',
                'surname' => 'Kila',
                'date_of_birth' => '1985-03-15',
                'gender' => 'Male',
                'village' => 'Waigani',
                'ward_id' => 1,
                'llg_id' => 1,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+675 7111 2222',
                'email' => '<EMAIL>',
                'address' => 'Section 14, Waigani, NCD',
                'marital_status' => 'Married',
                'highest_education_id' => 3,
                'course_taken' => 'Agriculture Certificate',
                'id_photo' => 'public/uploads/farmers/peter_kila.jpg',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'created_by' => 15,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'updated_by' => null
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'farmer_code' => 'F002',
                'given_name' => 'Mary',
                'surname' => 'Temu',
                'date_of_birth' => '1978-07-22',
                'gender' => 'Female',
                'village' => 'Gerehu',
                'ward_id' => 2,
                'llg_id' => 1,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+675 7222 3333',
                'email' => '<EMAIL>',
                'address' => 'Stage 6, Gerehu, NCD',
                'marital_status' => 'Single',
                'highest_education_id' => 2,
                'course_taken' => null,
                'id_photo' => 'public/uploads/farmers/mary_temu.jpg',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 weeks')),
                'created_by' => 16,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 weeks')),
                'updated_by' => null
            ]
        ];

        // Dummy district coverage data
        $districtCoverage = [
            ['district_name' => 'National Capital District', 'user_count' => 12],
            ['district_name' => 'Central Province', 'user_count' => 8],
            ['district_name' => 'Western Province', 'user_count' => 10],
            ['district_name' => 'Gulf Province', 'user_count' => 6],
            ['district_name' => 'Milne Bay Province', 'user_count' => 9]
        ];

        // Dummy crop farm blocks data following crops_farm_blocks schema
        $recentCropBlocks = [
            [
                'id' => 1,
                'exercise_id' => 1,
                'farmer_id' => 1,
                'crop_id' => 1,
                'block_code' => 'F8010001',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Waigani',
                'block_site' => 'Behind Community Center',
                'lon' => '147.1234',
                'lat' => '-9.4567',
                'remarks' => 'Good soil condition',
                'status' => 'active',
                'created_by' => 15,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'updated_by' => null,
                'updated_at' => null,
                'deleted_at' => null,
                'deleted_by' => null
            ],
            [
                'id' => 2,
                'exercise_id' => 1,
                'farmer_id' => 2,
                'crop_id' => 2,
                'block_code' => 'F8010002',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 2,
                'village' => 'Gerehu',
                'block_site' => 'Near Main Road',
                'lon' => '147.2345',
                'lat' => '-9.5678',
                'remarks' => 'Needs irrigation',
                'status' => 'active',
                'created_by' => 16,
                'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                'updated_by' => null,
                'updated_at' => null,
                'deleted_at' => null,
                'deleted_by' => null
            ]
        ];

        // Dummy livestock farm blocks data following livestock_farm_blocks schema
        $recentLivestockBlocks = [
            [
                'id' => 1,
                'exercise_id' => 1,
                'farmer_id' => 1,
                'block_code' => 'L8010001',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Waigani',
                'block_site' => 'Backyard Pen',
                'lon' => '147.1234',
                'lat' => '-9.4567',
                'remarks' => 'Small scale poultry',
                'status' => 'active',
                'created_by' => 15,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'updated_by' => null,
                'updated_at' => null,
                'deleted_at' => null,
                'deleted_by' => null
            ]
        ];

        // Dummy top permission users
        $topPermissionUsers = [
            ['name' => 'Admin User', 'role' => 'user', 'permission_count' => 25],
            ['name' => 'Supervisor One', 'role' => 'user', 'permission_count' => 18],
            ['name' => 'Field Manager', 'role' => 'user', 'permission_count' => 15],
            ['name' => 'Data Analyst', 'role' => 'user', 'permission_count' => 12],
            ['name' => 'Regional Lead', 'role' => 'user', 'permission_count' => 10]
        ];

        // Dummy monthly registration trend (last 6 months)
        $monthlyRegistrations = [
            ['month' => date('M Y', strtotime('-5 months')), 'count' => 3],
            ['month' => date('M Y', strtotime('-4 months')), 'count' => 7],
            ['month' => date('M Y', strtotime('-3 months')), 'count' => 5],
            ['month' => date('M Y', strtotime('-2 months')), 'count' => 12],
            ['month' => date('M Y', strtotime('-1 month')), 'count' => 8],
            ['month' => date('M Y'), 'count' => 10]
        ];

        $data = [
            'title' => 'Admin Dashboard',
            'page_header' => 'Admin Dashboard',
            'page_desc' => 'Organization Management Overview',
            'menu' => 'admin-dashboard',
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'usersByRole' => $usersByRole,
            'districtCoverage' => $districtCoverage,
            'topPermissionUsers' => $topPermissionUsers,
            'monthlyRegistrations' => $monthlyRegistrations,
            'recentFarmers' => $recentFarmers,
            'recentCropBlocks' => $recentCropBlocks,
            'recentLivestockBlocks' => $recentLivestockBlocks,
            'cropsData' => $cropsData,
            'livestockData' => $livestockData,
            'cropProductionTrends' => $cropProductionTrends,
            'livestockPopulationTrends' => $livestockPopulationTrends
        ];

        return view('admin_dashboard/admin_dashboard_index', $data);
    }


}
