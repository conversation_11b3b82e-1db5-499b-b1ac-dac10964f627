<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">
                    <i class="fas fa-users"></i> Farmers Report
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item active">Farmers Report</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($stats['total_farmers']) ?></h3>
                        <p>Total Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($stats['active_farmers']) ?></h3>
                        <p>Active Farmers</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?= number_format($stats['total_crop_blocks']) ?></h3>
                        <p>Crop Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3><?= number_format($stats['total_livestock_blocks']) ?></h3>
                        <p>Livestock Blocks</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-horse"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmers List Card -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list"></i> Farmers List
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-tool" data-card-widget="collapse">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <div class="table-responsive">
                    <table id="farmersTable" class="table table-bordered table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Photo</th>
                                <th>Farmer Code</th>
                                <th>Name</th>
                                <th>Gender</th>
                                <th>Province</th>
                                <th>District</th>
                                <th>LLG</th>
                                <th>Ward</th>
                                <th>Village</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Crop Blocks</th>
                                <th>Livestock Blocks</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($farmers as $farmer): ?>
                            <tr>
                                <td class="text-center">
                                    <?php if ($farmer['id_photo']): ?>
                                        <img src="<?= imgcheck($farmer['id_photo']) ?>" 
                                             class="img-circle elevation-2" 
                                             style="width: 40px; height: 40px; object-fit: cover;" 
                                             alt="Farmer Photo">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong class="text-primary"><?= esc($farmer['farmer_code']) ?></strong>
                                </td>
                                <td>
                                    <strong><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></strong>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $farmer['gender'] === 'Male' ? 'primary' : 'pink' ?>">
                                        <i class="fas fa-<?= $farmer['gender'] === 'Male' ? 'mars' : 'venus' ?>"></i>
                                        <?= esc($farmer['gender']) ?>
                                    </span>
                                </td>
                                <td><?= esc($farmer['province_name'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['district_name'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['llg_name'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['ward_name'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['village'] ?? 'N/A') ?></td>
                                <td>
                                    <?php if ($farmer['phone']): ?>
                                        <a href="tel:<?= esc($farmer['phone']) ?>" class="text-success">
                                            <i class="fas fa-phone"></i> <?= esc($farmer['phone']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($farmer['email']): ?>
                                        <a href="mailto:<?= esc($farmer['email']) ?>" class="text-info">
                                            <i class="fas fa-envelope"></i> <?= esc($farmer['email']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-success">
                                        <?= number_format($farmer['crop_blocks_count']) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-warning">
                                        <?= number_format($farmer['livestock_blocks_count']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $farmer['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst(esc($farmer['status'])) ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <a href="<?= base_url('admin/reports/farmers/' . $farmer['id']) ?>" 
                                       class="btn btn-sm btn-info" 
                                       title="View Profile">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
    </div>
</section>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#farmersTable').DataTable({
        responsive: true,
        order: [[1, 'desc']], // Sort by farmer code descending
        pageLength: 25,
        columnDefs: [
            {
                orderable: false,
                targets: [0, 14] // Photo and Actions columns
            },
            {
                className: 'text-center',
                targets: [0, 11, 12, 13, 14] // Photo, blocks count, status, actions columns
            }
        ],
        language: {
            search: "Search farmers:",
            lengthMenu: "Show _MENU_ farmers per page",
            info: "Showing _START_ to _END_ of _TOTAL_ farmers",
            infoEmpty: "No farmers found",
            infoFiltered: "(filtered from _MAX_ total farmers)"
        }
    });
});
</script>
<?= $this->endSection() ?>
