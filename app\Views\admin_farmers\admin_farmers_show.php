<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success">
                    <i class="fas fa-user"></i> Farmer Profile
                </h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/reports/farmers">Farmers Report</a></li>
                    <li class="breadcrumb-item active">Farmer Profile</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        
        <!-- Back Button -->
        <div class="row mb-3">
            <div class="col-12">
                <a href="<?= base_url('admin/reports/farmers') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Farmers List
                </a>
            </div>
        </div>

        <!-- Farmer Basic Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user-circle"></i> Basic Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Farmer Photo -->
                    <div class="col-md-3 text-center">
                        <?php if ($farmer['id_photo']): ?>
                            <img src="<?= imgcheck($farmer['id_photo']) ?>" 
                                 class="img-thumbnail mb-3" 
                                 style="max-width: 200px; max-height: 250px; object-fit: cover;" 
                                 alt="Farmer Photo">
                        <?php else: ?>
                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center mb-3" 
                                 style="width: 200px; height: 250px; margin: 0 auto;">
                                <i class="fas fa-user fa-5x text-white"></i>
                            </div>
                        <?php endif; ?>
                        
                        <h4 class="text-primary"><?= esc($farmer['farmer_code']) ?></h4>
                        <span class="badge badge-<?= $farmer['status'] === 'active' ? 'success' : 'secondary' ?> badge-lg">
                            <?= ucfirst(esc($farmer['status'])) ?>
                        </span>
                    </div>
                    
                    <!-- Farmer Details -->
                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Full Name:</strong></td>
                                        <td><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Gender:</strong></td>
                                        <td>
                                            <span class="badge badge-<?= $farmer['gender'] === 'Male' ? 'primary' : 'pink' ?>">
                                                <i class="fas fa-<?= $farmer['gender'] === 'Male' ? 'mars' : 'venus' ?>"></i>
                                                <?= esc($farmer['gender']) ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Date of Birth:</strong></td>
                                        <td><?= $farmer['date_of_birth'] ? date('F j, Y', strtotime($farmer['date_of_birth'])) : 'N/A' ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Marital Status:</strong></td>
                                        <td><?= esc($farmer['marital_status'] ?? 'N/A') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>
                                            <?php if ($farmer['phone']): ?>
                                                <a href="tel:<?= esc($farmer['phone']) ?>" class="text-success">
                                                    <i class="fas fa-phone"></i> <?= esc($farmer['phone']) ?>
                                                </a>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>
                                            <?php if ($farmer['email']): ?>
                                                <a href="mailto:<?= esc($farmer['email']) ?>" class="text-info">
                                                    <i class="fas fa-envelope"></i> <?= esc($farmer['email']) ?>
                                                </a>
                                            <?php else: ?>
                                                N/A
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Country:</strong></td>
                                        <td><?= esc($farmer['country_name'] ?? 'N/A') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Province:</strong></td>
                                        <td><?= esc($farmer['province_name'] ?? 'N/A') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>District:</strong></td>
                                        <td><?= esc($farmer['district_name'] ?? 'N/A') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>LLG:</strong></td>
                                        <td><?= esc($farmer['llg_name'] ?? 'N/A') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ward:</strong></td>
                                        <td><?= esc($farmer['ward_name'] ?? 'N/A') ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Village:</strong></td>
                                        <td><?= esc($farmer['village'] ?? 'N/A') ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <?php if ($farmer['address']): ?>
                        <div class="row mt-3">
                            <div class="col-12">
                                <strong>Address:</strong><br>
                                <p class="text-muted"><?= esc($farmer['address']) ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="small-box bg-success">
                    <div class="inner">
                        <h3><?= number_format($totals['total_hectares'], 2) ?></h3>
                        <p>Total Hectares</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-chart-area"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-6">
                <div class="small-box bg-info">
                    <div class="inner">
                        <h3><?= number_format($totals['total_plants']) ?></h3>
                        <p>Total Plants</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-seedling"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-6">
                <div class="small-box bg-warning">
                    <div class="inner">
                        <h3><?= number_format($totals['total_animals']) ?></h3>
                        <p>Total Animals</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-horse"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-6">
                <div class="small-box bg-danger">
                    <div class="inner">
                        <h3>K<?= number_format($totals['total_marketing_value'], 2) ?></h3>
                        <p>Marketing Value</p>
                    </div>
                    <div class="icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crop Farm Blocks -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-seedling"></i> Crop Farm Blocks (<?= count($cropBlocks) ?>)
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($cropBlocks)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Block Code</th>
                                <th>Crop</th>
                                <th>Block Site</th>
                                <th>Village</th>
                                <th>GPS Coordinates</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cropBlocks as $block): ?>
                            <tr>
                                <td><strong class="text-primary"><?= esc($block['block_code']) ?></strong></td>
                                <td>
                                    <span class="badge" style="background-color: <?= esc($block['crop_color_code'] ?? '#6c757d') ?>; color: white;">
                                        <?= esc($block['crop_name'] ?? 'N/A') ?>
                                    </span>
                                </td>
                                <td><?= esc($block['block_site']) ?></td>
                                <td><?= esc($block['village']) ?></td>
                                <td>
                                    <?php if ($block['lat'] && $block['lon']): ?>
                                        <small class="text-muted">
                                            Lat: <?= esc($block['lat']) ?><br>
                                            Lon: <?= esc($block['lon']) ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $block['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst(esc($block['status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M j, Y', strtotime($block['created_at'])) ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No crop farm blocks found for this farmer.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Livestock Farm Blocks -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-horse"></i> Livestock Farm Blocks (<?= count($livestockBlocks) ?>)
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($livestockBlocks)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Block Code</th>
                                <th>Block Site</th>
                                <th>Village</th>
                                <th>GPS Coordinates</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($livestockBlocks as $block): ?>
                            <tr>
                                <td><strong class="text-primary"><?= esc($block['block_code']) ?></strong></td>
                                <td><?= esc($block['block_site']) ?></td>
                                <td><?= esc($block['village']) ?></td>
                                <td>
                                    <?php if ($block['lat'] && $block['lon']): ?>
                                        <small class="text-muted">
                                            Lat: <?= esc($block['lat']) ?><br>
                                            Lon: <?= esc($block['lon']) ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $block['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst(esc($block['status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M j, Y', strtotime($block['created_at'])) ?>
                                    </small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-horse fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No livestock farm blocks found for this farmer.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Crops Data -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-leaf"></i> Crops Data (<?= count($cropsData) ?>)
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($cropsData)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Block Code</th>
                                <th>Crop</th>
                                <th>Breed</th>
                                <th>Plants</th>
                                <th>Hectares</th>
                                <th>Action Type</th>
                                <th>Action Date</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($cropsData as $data): ?>
                            <tr>
                                <td><strong class="text-primary"><?= esc($data['block_code']) ?></strong></td>
                                <td>
                                    <span class="badge" style="background-color: <?= esc($data['crop_color_code'] ?? '#6c757d') ?>; color: white;">
                                        <?= esc($data['crop_name'] ?? 'N/A') ?>
                                    </span>
                                </td>
                                <td><?= esc($data['breed'] ?? 'N/A') ?></td>
                                <td class="text-right"><?= number_format($data['number_of_plants']) ?></td>
                                <td class="text-right"><?= number_format($data['hectares'], 2) ?></td>
                                <td>
                                    <span class="badge badge-<?= $data['action_type'] === 'add' ? 'success' : 'warning' ?>">
                                        <?= ucfirst(esc($data['action_type'])) ?>
                                    </span>
                                </td>
                                <td><?= date('M j, Y', strtotime($data['action_date'])) ?></td>
                                <td>
                                    <span class="badge badge-<?= $data['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst(esc($data['status'])) ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-leaf fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No crops data found for this farmer.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Livestock Data -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-paw"></i> Livestock Data (<?= count($livestockData) ?>)
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($livestockData)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Block Code</th>
                                <th>Livestock</th>
                                <th>Breed</th>
                                <th>Male Count</th>
                                <th>Female Count</th>
                                <th>Total</th>
                                <th>Pasture Type</th>
                                <th>Growth Stage</th>
                                <th>Cost per Animal</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($livestockData as $data): ?>
                            <tr>
                                <td><strong class="text-primary"><?= esc($data['block_code']) ?></strong></td>
                                <td>
                                    <span class="badge" style="background-color: <?= esc($data['livestock_color_code'] ?? '#6c757d') ?>; color: white;">
                                        <?= esc($data['livestock_name'] ?? 'N/A') ?>
                                    </span>
                                </td>
                                <td><?= esc($data['breed'] ?? 'N/A') ?></td>
                                <td class="text-right"><?= number_format($data['he_total']) ?></td>
                                <td class="text-right"><?= number_format($data['she_total']) ?></td>
                                <td class="text-right"><strong><?= number_format($data['he_total'] + $data['she_total']) ?></strong></td>
                                <td><?= esc($data['pasture_type'] ?? 'N/A') ?></td>
                                <td><?= esc($data['growth_stage'] ?? 'N/A') ?></td>
                                <td class="text-right">
                                    <?php if ($data['cost_per_livestock']): ?>
                                        K<?= number_format($data['cost_per_livestock'], 2) ?>
                                    <?php else: ?>
                                        N/A
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $data['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst(esc($data['status'])) ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-paw fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No livestock data found for this farmer.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Marketing Data -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shopping-cart"></i> Marketing Data (<?= count($marketingData) ?>)
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($marketingData)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Market Date</th>
                                <th>Crop</th>
                                <th>Product</th>
                                <th>Buyer</th>
                                <th>Quantity</th>
                                <th>Unit</th>
                                <th>Price per Unit</th>
                                <th>Total Value</th>
                                <th>Market Stage</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($marketingData as $data): ?>
                            <tr>
                                <td><?= date('M j, Y', strtotime($data['market_date'])) ?></td>
                                <td><?= esc($data['crop_name'] ?? 'N/A') ?></td>
                                <td><?= esc($data['product'] ?? 'N/A') ?></td>
                                <td><?= esc($data['buyer_name'] ?? 'N/A') ?></td>
                                <td class="text-right"><?= number_format($data['quantity'], 2) ?></td>
                                <td><?= esc($data['unit'] ?? 'N/A') ?></td>
                                <td class="text-right">K<?= number_format($data['market_price_per_unit'], 2) ?></td>
                                <td class="text-right">
                                    <strong>K<?= number_format($data['quantity'] * $data['market_price_per_unit'], 2) ?></strong>
                                </td>
                                <td><?= esc($data['market_stage'] ?? 'N/A') ?></td>
                                <td>
                                    <span class="badge badge-<?= $data['status'] === 'active' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst(esc($data['status'])) ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No marketing data found for this farmer.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

    </div>
</section>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTables for tables if needed
    $('.table').DataTable({
        responsive: true,
        pageLength: 10,
        order: [[0, 'asc']]
    });
});
</script>
<?= $this->endSection() ?>
