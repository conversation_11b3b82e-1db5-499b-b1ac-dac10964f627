<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<section class="container-fluid">
    <!-- Hero Section -->
    <div class="row p-4 bg-gradient-primary" style="background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);">
        <div class="col-md-8 d-flex align-items-center">
            <div class="text-white p-3">
                <h1 class="display-5 font-weight-bold">Farmer Profile</h1>
                <p class="lead mb-3"><?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?> - <?= esc($farmer['farmer_code']) ?></p>
                <a href="<?= base_url('farmer-search') ?>" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Search
                </a>
            </div>
        </div>
        <div class="col-md-4 text-center">
            <i class="fas fa-user-circle fa-5x text-white opacity-50"></i>
        </div>
    </div>

    <!-- Profile Section -->
    <div class="row p-4">
        <div class="col-12">
            <!-- Farmer Profile Header -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <?php if (!empty($farmer['id_photo'])): ?>
                                <img src="<?= base_url($farmer['id_photo']) ?>"
                                     alt="Farmer Photo"
                                     class="img-fluid rounded-circle shadow mb-3"
                                     style="width: 150px; height: 150px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3 shadow"
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-4x"></i>
                                </div>
                            <?php endif; ?>
                            <h4 class="text-success"><?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?></h4>
                            <p class="text-muted">Farmer Code: <span class="badge bg-success"><?= esc($farmer['farmer_code']) ?></span></p>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-success mb-3">Contact Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td><?= !empty($farmer['phone']) ? esc($farmer['phone']) : 'Not provided' ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td><?= !empty($farmer['email']) ? esc($farmer['email']) : 'Not provided' ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="text-success mb-3">Location Information</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Country:</strong></td>
                                            <td><?= esc($farmer['country_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Province:</strong></td>
                                            <td><?= esc($farmer['province_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>District:</strong></td>
                                            <td><?= esc($farmer['district_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>LLG:</strong></td>
                                            <td><?= esc($farmer['llg_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Ward:</strong></td>
                                            <td><?= esc($farmer['ward_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Village:</strong></td>
                                            <td><?= esc($farmer['village']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>

            <!-- Summary Statistics -->
            <div class="row g-4 mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center border-0 shadow-sm hover-shadow">
                        <div class="card-body">
                            <i class="fas fa-seedling fa-2x mb-2"></i>
                            <h4><?= count($crop_blocks) ?></h4>
                            <p class="mb-0">Crop Blocks</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center border-0 shadow-sm hover-shadow">
                        <div class="card-body">
                            <i class="fas fa-warehouse fa-2x mb-2"></i>
                            <h4><?= count($livestock_blocks) ?></h4>
                            <p class="mb-0">Livestock Blocks</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center border-0 shadow-sm hover-shadow">
                        <div class="card-body">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <h4><?= $total_marketing_count ?></h4>
                            <p class="mb-0">Marketing Records</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white text-center border-0 shadow-sm hover-shadow">
                        <div class="card-body">
                            <i class="fas fa-calendar fa-2x mb-2"></i>
                            <h4><?= date('Y', strtotime($farmer['created_at'])) ?></h4>
                            <p class="mb-0">Registered Year</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Farming Area -->
            <div class="row mb-4">
                <div class="col-md-6 mx-auto">
                    <div class="card border-0 shadow-sm hover-shadow">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-area me-2"></i>
                                Total Farming Area
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="display-4 text-success mb-2">
                                <?= number_format($total_hectares, 2) ?>
                            </div>
                            <h5 class="text-muted">Hectares</h5>
                            <p class="text-muted mb-0">
                                Total cultivated area across all crop blocks
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Farming Activities -->
            <div class="row g-4">
                <!-- Crop Farm Blocks -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm hover-shadow h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-seedling me-2"></i>
                                Crop Farm Blocks (<?= count($crop_blocks) ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                        <?php if (!empty($crop_blocks)): ?>
                            <?php foreach ($crop_blocks as $block): ?>
                                <div class="border rounded p-3 mb-3 bg-light">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="text-primary mb-1">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?= esc($block['block_code']) ?>
                                            </h6>
                                            <p class="mb-1">
                                                <strong>Crop:</strong> 
                                                <span class="badge" style="background-color: <?= esc($block['crop_color_code']) ?>;">
                                                    <?= esc($block['crop_name']) ?>
                                                </span>
                                            </p>
                                            <p class="mb-1"><strong>Site:</strong> <?= esc($block['block_site']) ?></p>
                                            <p class="mb-1"><strong>Village:</strong> <?= esc($block['village']) ?></p>
                                            <?php if (!empty($block['total_hectares'])): ?>
                                                <p class="mb-0">
                                                    <strong>Area:</strong>
                                                    <span class="badge bg-info"><?= number_format($block['total_hectares'], 2) ?> ha</span>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">
                                                Data Records: <?= $block['crops_data_count'] ?>
                                            </small>
                                            <?php if (!empty($block['lat']) && !empty($block['lon'])): ?>
                                                <br><small class="text-success">
                                                    <i class="fas fa-map-marker-alt"></i> GPS Available
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-seedling fa-3x mb-3"></i>
                                <p>No crop farm blocks recorded</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

                <!-- Livestock Farm Blocks -->
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm hover-shadow h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-warehouse me-2"></i>
                                Livestock Farm Blocks (<?= count($livestock_blocks) ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                        <?php if (!empty($livestock_blocks)): ?>
                            <?php foreach ($livestock_blocks as $block): ?>
                                <div class="border rounded p-3 mb-3 bg-light">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="text-warning mb-1">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                <?= esc($block['block_code']) ?>
                                            </h6>
                                            <p class="mb-1"><strong>Site:</strong> <?= esc($block['block_site']) ?></p>
                                            <p class="mb-0"><strong>Village:</strong> <?= esc($block['village']) ?></p>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">
                                                Data Records: <?= $block['livestock_data_count'] ?>
                                            </small>
                                            <?php if (!empty($block['lat']) && !empty($block['lon'])): ?>
                                                <br><small class="text-success">
                                                    <i class="fas fa-map-marker-alt"></i> GPS Available
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-cow fa-3x mb-3"></i>
                                <p>No livestock farm blocks recorded</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

            <!-- Marketing Activities -->
            <?php if (!empty($marketing_data)): ?>
                <div class="col-12">
                    <div class="card border-0 shadow-sm hover-shadow mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Recent Marketing Activities (<?= count($marketing_data) ?>/<?= $total_marketing_count ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Product</th>
                                    <th>Buyer</th>
                                    <th>Quantity</th>
                                    <th>Price per Unit</th>
                                    <th>Location</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($marketing_data as $market): ?>
                                    <tr>
                                        <td><?= date('M j, Y', strtotime($market['market_date'])) ?></td>
                                        <td>
                                            <?= esc($market['product']) ?>
                                            <?php if (!empty($market['crop_name'])): ?>
                                                <br><small class="text-muted"><?= esc($market['crop_name']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= esc($market['buyer_name']) ?></td>
                                        <td><?= esc($market['quantity']) ?> <?= esc($market['unit_of_measure']) ?></td>
                                        <td>K<?= number_format($market['market_price_per_unit'], 2) ?></td>
                                        <td><?= esc($market['selling_location']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- GPS Map Section -->
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card border-0 shadow-sm hover-shadow">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marked-alt me-2"></i>
                            Farm Blocks Location Map
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($gps_locations)): ?>
                            <div id="farmMap" style="height: 400px; border-radius: 10px;"></div>
                            <div class="mt-3 text-center">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Showing <?= count($gps_locations) ?> blocks with GPS coordinates
                                </small>
                            </div>
                        <?php else: ?>
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-map-marked-alt fa-3x mb-3"></i>
                                <h6>No GPS Data Available</h6>
                                <p class="mb-0">Farm blocks don't have GPS coordinates recorded</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add custom styles matching home page theme -->
<style>
    .hover-shadow:hover {
        transform: translateY(-5px);
        transition: all 0.3s ease;
        box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
    }

    .feature-icon {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bg-gradient-primary {
        background-size: 200% 200%;
        animation: gradientAnimation 15s ease infinite;
    }

    @keyframes gradientAnimation {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .btn-success {
        background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
        border: none;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%) !important;
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>

<?php if (!empty($gps_locations)): ?>
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
        crossorigin=""></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // GPS locations data from PHP
    const gpsLocations = <?= json_encode($gps_locations) ?>;

    if (gpsLocations.length > 0) {
        // Calculate center point
        let centerLat = 0, centerLon = 0;
        gpsLocations.forEach(location => {
            centerLat += location.lat;
            centerLon += location.lon;
        });
        centerLat /= gpsLocations.length;
        centerLon /= gpsLocations.length;

        // Initialize map with a default zoom
        const map = L.map('farmMap').setView([centerLat, centerLon], 10);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(map);

        // Create marker group for bounds calculation
        const markerGroup = L.featureGroup();

        // Add markers for each location
        gpsLocations.forEach(location => {
            const icon = location.type === 'crop' ?
                L.divIcon({
                    html: '<i class="fas fa-seedling" style="color: #28a745; font-size: 14px;"></i>',
                    iconSize: [24, 24],
                    className: 'custom-div-icon'
                }) :
                L.divIcon({
                    html: '<i class="fas fa-warehouse" style="color: #17a2b8; font-size: 14px;"></i>',
                    iconSize: [24, 24],
                    className: 'custom-div-icon'
                });

            const popupContent = location.type === 'crop' ?
                `<div class="text-center">
                    <h6 class="text-success mb-2">${location.block_code}</h6>
                    <p class="mb-1"><strong>Crop:</strong> ${location.crop_name}</p>
                    <p class="mb-1"><strong>Site:</strong> ${location.site}</p>
                    <p class="mb-1"><strong>Village:</strong> ${location.village}</p>
                    ${location.hectares > 0 ? `<p class="mb-0"><strong>Area:</strong> ${location.hectares} ha</p>` : ''}
                    <small class="text-muted">Lat: ${location.lat.toFixed(6)}, Lon: ${location.lon.toFixed(6)}</small>
                </div>` :
                `<div class="text-center">
                    <h6 class="text-info mb-2">${location.block_code}</h6>
                    <p class="mb-1"><strong>Site:</strong> ${location.site}</p>
                    <p class="mb-1"><strong>Village:</strong> ${location.village}</p>
                    <small class="text-muted">Lat: ${location.lat.toFixed(6)}, Lon: ${location.lon.toFixed(6)}</small>
                </div>`;

            const marker = L.marker([location.lat, location.lon], {icon: icon})
                .bindPopup(popupContent);

            // Add marker to both map and group
            marker.addTo(map);
            markerGroup.addLayer(marker);
        });

        // Auto-fit map to show all markers with proper padding
        if (gpsLocations.length === 1) {
            // Single location - set appropriate zoom level
            map.setView([gpsLocations[0].lat, gpsLocations[0].lon], 15);
        } else if (gpsLocations.length > 1) {
            // Multiple locations - fit bounds with padding
            const bounds = markerGroup.getBounds();
            map.fitBounds(bounds, {
                padding: [20, 20], // Add padding around the bounds
                maxZoom: 16 // Prevent zooming in too much
            });
        }

        // Add a scale control
        L.control.scale().addTo(map);

        // Log GPS locations for debugging
        console.log('GPS Locations:', gpsLocations);
        console.log('Map bounds:', markerGroup.getBounds());
    }
});
</script>

<style>
.custom-div-icon {
    background: white;
    border: 2px solid #ddd;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-div-icon i {
    margin: 0;
    padding: 0;
}
</style>
<?php endif; ?>

<?= $this->endSection() ?>
