<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><?= $page_header ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item active">Admin Dashboard</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        
        <!-- Info boxes -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-info elevation-1"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Users</span>
                        <span class="info-box-number"><?= number_format($stats['total_users']) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: <?= $stats['total_users'] > 0 ? ($stats['active_users'] / $stats['total_users']) * 100 : 0 ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?= $stats['active_users'] ?> Active Users
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-user-shield"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Admin Users</span>
                        <span class="info-box-number"><?= number_format($stats['admin_users']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-user-tie"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Supervisor Users</span>
                        <span class="info-box-number"><?= number_format($stats['supervisor_users']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box mb-3">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-user-friends"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Field Users</span>
                        <span class="info-box-number"><?= number_format($stats['field_users']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farmers and Farm Blocks Row -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-seedling"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Farmers</span>
                        <span class="info-box-number"><?= number_format($stats['total_farmers']) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?= $stats['total_farmers'] > 0 ? ($stats['active_farmers'] / $stats['total_farmers']) * 100 : 0 ?>%"></div>
                        </div>
                        <span class="progress-description">
                            <?= $stats['active_farmers'] ?> Active Farmers
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-map"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Crop Farm Blocks</span>
                        <span class="info-box-number"><?= number_format($stats['total_crop_blocks']) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-md-4">
                <div class="info-box">
                    <span class="info-box-icon bg-secondary elevation-1"><i class="fas fa-cow"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Livestock Blocks</span>
                        <span class="info-box-number"><?= number_format($stats['total_livestock_blocks']) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Agricultural Statistics -->
        <div class="row">
            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-success elevation-1"><i class="fas fa-seedling"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Crop Types</span>
                        <span class="info-box-number"><?= count($cropsData) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                        <span class="progress-description">
                            <?= array_sum(array_column($cropsData, 'total_hectares')) ?> total hectares
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-warning elevation-1"><i class="fas fa-horse-head"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Livestock Types</span>
                        <span class="info-box-number"><?= count($livestockData) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 100%"></div>
                        </div>
                        <span class="progress-description">
                            <?= array_sum(array_map(function($l) { return $l['total_male'] + $l['total_female']; }, $livestockData)) ?> total animals
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-primary elevation-1"><i class="fas fa-chart-line"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Avg Crop Yield</span>
                        <span class="info-box-number"><?= number_format(array_sum(array_column($cropsData, 'avg_yield_per_hectare')) / count($cropsData), 1) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-primary" style="width: 85%"></div>
                        </div>
                        <span class="progress-description">
                            tons per hectare
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-6 col-md-3">
                <div class="info-box">
                    <span class="info-box-icon bg-danger elevation-1"><i class="fas fa-dollar-sign"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Avg Market Value</span>
                        <span class="info-box-number">K<?= number_format(array_sum(array_column($livestockData, 'avg_market_price')) / count($livestockData), 0) ?></span>
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: 75%"></div>
                        </div>
                        <span class="progress-description">
                            per livestock head
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Data Row -->
        <div class="row">
            <!-- User Registration Trend Chart -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">User Registration Trend (Last 6 Months)</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="registrationChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>

            <!-- User Distribution by Role -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">User Distribution by Role</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="roleChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crops and Livestock Data Section -->
        <div class="row">
            <!-- Crop Production Trends -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Crop Production Trends (Hectares)</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="cropTrendsChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>

            <!-- Livestock Population Trends -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Livestock Population Trends</h3>
                    </div>
                    <div class="card-body">
                        <canvas id="livestockTrendsChart" style="height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crops and Livestock Summary Cards -->
        <div class="row">
            <!-- Top Crops Summary -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Top Crops by Production</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach (array_slice($cropsData, 0, 3) as $crop): ?>
                            <div class="col-md-4 col-sm-6 col-12">
                                <div class="info-box bg-light">
                                    <span class="info-box-icon" style="background-color: <?= $crop['crop_color_code'] ?>;">
                                        <i class="<?= $crop['crop_icon'] ?> text-white"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= esc($crop['crop_name']) ?></span>
                                        <span class="info-box-number"><?= number_format($crop['total_hectares'], 1) ?> ha</span>
                                        <div class="progress">
                                            <div class="progress-bar" style="background-color: <?= $crop['crop_color_code'] ?>; width: <?= ($crop['total_hectares'] / 1600) * 100 ?>%"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?= number_format($crop['total_blocks']) ?> blocks
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Livestock Summary -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Livestock Population Summary</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach (array_slice($livestockData, 0, 4) as $livestock): ?>
                            <div class="col-md-6 col-sm-6 col-12">
                                <div class="info-box bg-light">
                                    <span class="info-box-icon" style="background-color: <?= $livestock['color_code'] ?>;">
                                        <i class="<?= $livestock['icon'] ?> text-white"></i>
                                    </span>
                                    <div class="info-box-content">
                                        <span class="info-box-text"><?= esc($livestock['name']) ?></span>
                                        <span class="info-box-number"><?= number_format($livestock['total_male'] + $livestock['total_female']) ?></span>
                                        <div class="progress">
                                            <div class="progress-bar" style="background-color: <?= $livestock['color_code'] ?>; width: <?= (($livestock['total_male'] + $livestock['total_female']) / 10000) * 100 ?>%"></div>
                                        </div>
                                        <span class="progress-description">
                                            <?= number_format($livestock['total_blocks']) ?> blocks
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Users and Recent Farmers -->
        <div class="row">
            <!-- Recent Users -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Users</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Sys No</th>
                                    <th>Name</th>
                                    <th>Position</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recentUsers)): ?>
                                    <?php foreach ($recentUsers as $user): ?>
                                        <tr>
                                            <td><?= esc($user['sys_no']) ?></td>
                                            <td>
                                                <strong><?= esc($user['name']) ?></strong><br>
                                                <small class="text-muted"><?= esc($user['email']) ?></small>
                                            </td>
                                            <td><?= esc($user['position']) ?></td>
                                            <td>
                                                <?php if ($user['is_admin'] == 1): ?>
                                                    <span class="badge badge-danger">Admin</span>
                                                <?php elseif ($user['is_supervisor'] == 1): ?>
                                                    <span class="badge badge-warning">Supervisor</span>
                                                <?php else: ?>
                                                    <span class="badge badge-info"><?= ucfirst($user['role']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['status'] == 1): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center">No users found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Farmers -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Farmers</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Village</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recentFarmers)): ?>
                                    <?php foreach ($recentFarmers as $farmer): ?>
                                        <tr>
                                            <td><?= esc($farmer['farmer_code']) ?></td>
                                            <td>
                                                <strong><?= esc($farmer['given_name'] . ' ' . $farmer['surname']) ?></strong><br>
                                                <small class="text-muted"><?= esc($farmer['gender']) ?>, <?= date('Y') - date('Y', strtotime($farmer['date_of_birth'])) ?> years</small>
                                            </td>
                                            <td><?= esc($farmer['village']) ?></td>
                                            <td>
                                                <?php if ($farmer['status'] == 'active'): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No farmers found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Farm Blocks Overview -->
        <div class="row">
            <!-- Recent Crop Blocks -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Crop Farm Blocks</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Block Code</th>
                                    <th>Village</th>
                                    <th>Site</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recentCropBlocks)): ?>
                                    <?php foreach ($recentCropBlocks as $block): ?>
                                        <tr>
                                            <td><?= esc($block['block_code']) ?></td>
                                            <td><?= esc($block['village']) ?></td>
                                            <td><?= esc($block['block_site']) ?></td>
                                            <td>
                                                <?php if ($block['status'] == 'active'): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No crop blocks found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Livestock Blocks -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Livestock Farm Blocks</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Block Code</th>
                                    <th>Village</th>
                                    <th>Site</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($recentLivestockBlocks)): ?>
                                    <?php foreach ($recentLivestockBlocks as $block): ?>
                                        <tr>
                                            <td><?= esc($block['block_code']) ?></td>
                                            <td><?= esc($block['village']) ?></td>
                                            <td><?= esc($block['block_site']) ?></td>
                                            <td>
                                                <?php if ($block['status'] == 'active'): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No livestock blocks found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- District Coverage and Top Permission Users -->
        <div class="row">

            <!-- District Coverage -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">District Coverage</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>District</th>
                                    <th>Active Users</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($districtCoverage)): ?>
                                    <?php foreach ($districtCoverage as $district): ?>
                                        <tr>
                                            <td><?= esc($district['district_name']) ?></td>
                                            <td><span class="badge badge-primary"><?= $district['user_count'] ?></span></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="2" class="text-center">No district coverage data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Permission Users -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Users with Most Permissions</h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Permissions Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($topPermissionUsers)): ?>
                                    <?php foreach ($topPermissionUsers as $user): ?>
                                        <tr>
                                            <td><?= esc($user['name']) ?></td>
                                            <td>
                                                <?php if ($user['role'] == 'admin'): ?>
                                                    <span class="badge badge-danger">Admin</span>
                                                <?php elseif ($user['role'] == 'supervisor'): ?>
                                                    <span class="badge badge-warning">Supervisor</span>
                                                <?php else: ?>
                                                    <span class="badge badge-info"><?= ucfirst($user['role']) ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td><span class="badge badge-success"><?= $user['permission_count'] ?></span></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">No permission data available</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Quick Actions</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users/create') ?>" class="btn btn-app">
                                    <i class="fas fa-user-plus"></i> Add User
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/users') ?>" class="btn btn-app">
                                    <i class="fas fa-users"></i> Manage Users
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/reports') ?>" class="btn btn-app">
                                    <i class="fas fa-chart-bar"></i> View Reports
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?= base_url('admin/settings') ?>" class="btn btn-app">
                                    <i class="fas fa-cog"></i> Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Registration Trend Chart
    const registrationData = <?= json_encode($monthlyRegistrations) ?>;
    const registrationCtx = document.getElementById('registrationChart').getContext('2d');
    new Chart(registrationCtx, {
        type: 'line',
        data: {
            labels: registrationData.map(item => item.month),
            datasets: [{
                label: 'New Users',
                data: registrationData.map(item => item.count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Role Distribution Chart
    const roleData = <?= json_encode($usersByRole) ?>;
    const roleCtx = document.getElementById('roleChart').getContext('2d');
    new Chart(roleCtx, {
        type: 'doughnut',
        data: {
            labels: roleData.map(item => item.role.charAt(0).toUpperCase() + item.role.slice(1)),
            datasets: [{
                data: roleData.map(item => item.count),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Crop Production Trends Chart
    const cropTrendsData = <?= json_encode($cropProductionTrends) ?>;
    const cropTrendsCtx = document.getElementById('cropTrendsChart').getContext('2d');
    new Chart(cropTrendsCtx, {
        type: 'line',
        data: {
            labels: cropTrendsData.map(item => item.month),
            datasets: [
                {
                    label: 'Sweet Potato',
                    data: cropTrendsData.map(item => item.sweet_potato),
                    borderColor: '#FF6B35',
                    backgroundColor: 'rgba(255, 107, 53, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Banana',
                    data: cropTrendsData.map(item => item.banana),
                    borderColor: '#FFD23F',
                    backgroundColor: 'rgba(255, 210, 63, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Cocoa',
                    data: cropTrendsData.map(item => item.cocoa),
                    borderColor: '#8B4513',
                    backgroundColor: 'rgba(139, 69, 19, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Coffee',
                    data: cropTrendsData.map(item => item.coffee),
                    borderColor: '#6F4E37',
                    backgroundColor: 'rgba(111, 78, 55, 0.1)',
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Hectares'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Livestock Population Trends Chart
    const livestockTrendsData = <?= json_encode($livestockPopulationTrends) ?>;
    const livestockTrendsCtx = document.getElementById('livestockTrendsChart').getContext('2d');
    new Chart(livestockTrendsCtx, {
        type: 'line',
        data: {
            labels: livestockTrendsData.map(item => item.month),
            datasets: [
                {
                    label: 'Chicken',
                    data: livestockTrendsData.map(item => item.chicken),
                    borderColor: '#FFD700',
                    backgroundColor: 'rgba(255, 215, 0, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Pig',
                    data: livestockTrendsData.map(item => item.pig),
                    borderColor: '#FFC0CB',
                    backgroundColor: 'rgba(255, 192, 203, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Goat',
                    data: livestockTrendsData.map(item => item.goat),
                    borderColor: '#DEB887',
                    backgroundColor: 'rgba(222, 184, 135, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Duck',
                    data: livestockTrendsData.map(item => item.duck),
                    borderColor: '#87CEEB',
                    backgroundColor: 'rgba(135, 206, 235, 0.1)',
                    tension: 0.1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Population'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>
