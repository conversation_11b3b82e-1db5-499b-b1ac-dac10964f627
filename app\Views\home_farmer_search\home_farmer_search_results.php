<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<section class="container-fluid">
    <!-- Hero Section -->
    <div class="row p-4 bg-gradient-primary" style="background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);">
        <div class="col-md-8 d-flex align-items-center">
            <div class="text-white p-3">
                <h1 class="display-5 font-weight-bold">Search Results</h1>
                <p class="lead mb-3">Farmer Code: <strong><?= esc($farmer_code) ?></strong></p>
                <a href="<?= base_url('farmer-search') ?>" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Search
                </a>
            </div>
        </div>
        <div class="col-md-4 text-center">
            <i class="fas fa-search-plus fa-5x text-white opacity-50"></i>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row p-4">
        <div class="col-md-10 mx-auto">
            <!-- Alert Messages -->
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            <?php if (isset($farmer) && $farmer): ?>
                <div class="alert alert-success border-0 shadow-sm mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle fa-lg text-success"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <strong>Farmer Found!</strong> Click "View Profile" to see detailed information.
                        </div>
                    </div>
                </div>

                <!-- Farmer Summary Card -->
                <div class="card border-0 shadow-sm hover-shadow mb-4">
                    <div class="card-body p-4">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <?php if (!empty($farmer['id_photo'])): ?>
                                    <img src="<?= base_url($farmer['id_photo']) ?>"
                                         alt="Farmer Photo"
                                         class="img-fluid rounded-circle shadow"
                                         style="width: 80px; height: 80px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center shadow"
                                         style="width: 80px; height: 80px;">
                                        <i class="fas fa-user fa-2x"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-7">
                                <h4 class="text-success mb-3">
                                    <?= esc($farmer['given_name']) ?> <?= esc($farmer['surname']) ?>
                                </h4>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <p class="mb-2">
                                            <strong>Farmer Code:</strong>
                                            <span class="badge bg-success"><?= esc($farmer['farmer_code']) ?></span>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Gender:</strong> <?= esc($farmer['gender']) ?>
                                        </p>
                                        <p class="mb-2">
                                            <strong>Village:</strong> <?= esc($farmer['village']) ?>
                                        </p>
                                    </div>
                                    <div class="col-sm-6">
                                        <p class="mb-2">
                                            <strong>Ward:</strong> <?= esc($farmer['ward_name']) ?>
                                        </p>
                                        <p class="mb-2">
                                            <strong>LLG:</strong> <?= esc($farmer['llg_name']) ?>
                                        </p>
                                        <p class="mb-2">
                                            <strong>District:</strong> <?= esc($farmer['district_name']) ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <a href="<?= base_url('farmer-search/profile/' . $farmer['id']) ?>"
                                   class="btn btn-success btn-lg shadow-sm">
                                    <i class="fas fa-user me-2"></i>
                                    View Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm hover-shadow text-center">
                            <div class="card-body p-4">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-map-marker-alt fa-3x text-success"></i>
                                </div>
                                <h5>Province</h5>
                                <p class="text-muted mb-0"><?= esc($farmer['province_name']) ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm hover-shadow text-center">
                            <div class="card-body p-4">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-phone fa-3x text-info"></i>
                                </div>
                                <h5>Contact</h5>
                                <p class="text-muted mb-0"><?= !empty($farmer['phone']) ? esc($farmer['phone']) : 'Not provided' ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm hover-shadow text-center">
                            <div class="card-body p-4">
                                <div class="feature-icon mb-3">
                                    <i class="fas fa-check-circle fa-3x text-success"></i>
                                </div>
                                <h5>Status</h5>
                                <p class="mb-0">
                                    <span class="badge bg-success">Active</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            <?php else: ?>
                <div class="alert alert-warning border-0 shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <strong>No farmer found</strong> with the provided farmer code: <?= esc($farmer_code) ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Search Again -->
            <div class="text-center mt-5">
                <a href="<?= base_url('farmer-search') ?>" class="btn btn-success btn-lg px-4">
                    <i class="fas fa-search me-2"></i>
                    Search Another Farmer
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Add custom styles matching home page theme -->
<style>
    .hover-shadow:hover {
        transform: translateY(-5px);
        transition: all 0.3s ease;
        box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
    }

    .feature-icon {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bg-gradient-primary {
        background-size: 200% 200%;
        animation: gradientAnimation 15s ease infinite;
    }

    @keyframes gradientAnimation {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .btn-success {
        background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
        border: none;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%) !important;
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>

<?= $this->endSection() ?>
