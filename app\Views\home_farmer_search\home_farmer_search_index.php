<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<section class="container-fluid">
    <!-- Search Section -->
    <div class="row p-4">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5">Farmer Search</h2>
            <p class="lead text-muted">Enter the farmer code to access detailed agricultural profiles</p>
        </div>

        <div class="col-md-8 col-lg-6 mx-auto">
            <div class="card h-100 border-0 shadow-sm hover-shadow">
                <div class="card-body p-5">
                    <!-- Alert Messages -->
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Search Form Header -->
                    <div class="text-center mb-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-search fa-3x text-success"></i>
                        </div>
                        <h3 class="text-dark mb-2">Search Farmer Profile</h3>
                        <p class="text-muted">Enter the farmer code to access comprehensive agricultural information</p>
                    </div>

                    <?= form_open('farmer-search', ['class' => 'needs-validation', 'novalidate' => true]) ?>
                        <div class="mb-4">
                            <label for="farmer_code" class="form-label fw-bold">
                                <i class="fas fa-id-card me-2 text-success"></i>
                                Farmer Code
                            </label>
                            <div class="input-group input-group-lg">
                                <span class="input-group-text bg-success text-white">
                                    <i class="fas fa-barcode"></i>
                                </span>
                                <input type="text"
                                       class="form-control"
                                       id="farmer_code"
                                       name="farmer_code"
                                       placeholder="Enter farmer code (e.g., F71401)"
                                       required
                                       value="<?= old('farmer_code') ?>"
                                       style="font-family: monospace; font-weight: 600;">
                                <div class="invalid-feedback">
                                    Please enter a farmer code.
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Farmer codes are in the format F7XXXX (e.g., F71401, F72001)
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-search me-2"></i>
                                Search Farmer Profile
                            </button>
                        </div>
                    <?= form_close() ?>

                    <div class="mt-4 text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            This search is for public access to farmer profiles
                        </small>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- How to Use Section -->
    <div class="row p-4 bg-light">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5">How It Works</h2>
            <p class="lead text-muted">Simple steps to access comprehensive farmer information</p>
        </div>

        <!-- Feature Cards -->
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm hover-shadow">
                <div class="card-body text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-edit fa-3x text-success"></i>
                    </div>
                    <h4>Enter Code</h4>
                    <p class="text-muted">Input the unique farmer code in the format F7XXXX to begin your search</p>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm hover-shadow">
                <div class="card-body text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-search fa-3x text-success"></i>
                    </div>
                    <h4>Search Profile</h4>
                    <p class="text-muted">Click search to find the farmer in our comprehensive database</p>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm hover-shadow">
                <div class="card-body text-center p-4">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-user-circle fa-3x text-info"></i>
                    </div>
                    <h4>View Details</h4>
                    <p class="text-muted">Access complete profile and detailed farming activities</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action Section -->
    <div class="row p-5 bg-success text-white text-center">
        <div class="col-12">
            <h2 class="mb-4">Need Help Finding a Farmer?</h2>
            <p class="lead mb-4">Contact our support team for assistance with farmer codes and profile access</p>
            <a href="<?= base_url() ?>about" class="btn btn-light btn-lg px-4 me-3">Learn More</a>
            <a href="<?= base_url() ?>login" class="btn btn-outline-light btn-lg px-4">Staff Login</a>
        </div>
    </div>
</section>

<!-- Add custom styles matching home page theme -->
<style>
    .hover-shadow:hover {
        transform: translateY(-5px);
        transition: all 0.3s ease;
        box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
    }

    .feature-icon {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bg-gradient-primary {
        background-size: 200% 200%;
        animation: gradientAnimation 15s ease infinite;
    }

    @keyframes gradientAnimation {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }

    .btn-success {
        background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
        border: none;
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #1b5e20 0%, #2e7d32 100%) !important;
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }

    .input-group-text.bg-success {
        background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%) !important;
    }
</style>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Auto-format farmer code input
document.getElementById('farmer_code').addEventListener('input', function(e) {
    let value = e.target.value.toUpperCase();
    // Remove any non-alphanumeric characters except for the initial F
    value = value.replace(/[^F0-9]/g, '');
    e.target.value = value;
});
</script>

<?= $this->endSection() ?>
