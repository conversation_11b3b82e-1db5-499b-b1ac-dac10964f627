<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxCountryModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\CropsModel;
use App\Models\CropBuyersModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\LivestockFarmDataModel;
use App\Models\CropsFarmMarketingDataModel;

class AdminFarmers extends BaseController
{
    protected $farmerModel;
    protected $provinceModel;
    protected $countryModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $cropsModel;
    protected $cropBuyersModel;
    protected $cropsFarmBlockModel;
    protected $livestockFarmBlockModel;
    protected $cropsFarmCropsDataModel;
    protected $livestockFarmDataModel;
    protected $cropsFarmMarketingDataModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        
        // Initialize all required models
        $this->farmerModel = new FarmerInformationModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->countryModel = new AdxCountryModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->cropsModel = new CropsModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->cropsFarmBlockModel = new CropsFarmBlockModel();
        $this->livestockFarmBlockModel = new LivestockFarmBlockModel();
        $this->cropsFarmCropsDataModel = new CropsFarmCropsDataModel();
        $this->livestockFarmDataModel = new LivestockFarmDataModel();
        $this->cropsFarmMarketingDataModel = new CropsFarmMarketingDataModel();
    }

    /**
     * Display farmers list with brief information
     * GET /admin/reports/farmers
     */
    public function index()
    {
        try {
            // Get farmers with location details and summary statistics
            $farmers = $this->getFarmersWithLocationAndStats();
            
            // Calculate summary statistics
            $stats = [
                'total_farmers' => count($farmers),
                'active_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'active')),
                'inactive_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'inactive')),
                'total_crop_blocks' => array_sum(array_column($farmers, 'crop_blocks_count')),
                'total_livestock_blocks' => array_sum(array_column($farmers, 'livestock_blocks_count')),
                'farmers_with_email' => count(array_filter($farmers, fn($f) => !empty($f['email']))),
                'farmers_with_phone' => count(array_filter($farmers, fn($f) => !empty($f['phone'])))
            ];

            $data = [
                'title' => 'Farmers Report',
                'page_header' => 'Farmers Report',
                'menu' => 'admin-reports-farmers',
                'farmers' => $farmers,
                'stats' => $stats
            ];

            return view('admin_farmers/admin_farmers_index', $data);

        } catch (\Exception $e) {
            log_message('error', 'AdminFarmers::index - ' . $e->getMessage());
            session()->setFlashdata('error', 'Error loading farmers data: ' . $e->getMessage());
            return redirect()->to(base_url('admin/dashboard'));
        }
    }

    /**
     * Display detailed farmer profile
     * GET /admin/reports/farmers/{id}
     */
    public function show($id)
    {
        try {
            // Get farmer basic information with location details
            $farmer = $this->getFarmerWithLocationDetails($id);
            
            if (!$farmer) {
                session()->setFlashdata('error', 'Farmer not found.');
                return redirect()->to(base_url('admin/reports/farmers'));
            }

            // Get farmer's crop farm blocks with crop details
            $cropBlocks = $this->getFarmerCropBlocks($id);
            
            // Get farmer's livestock farm blocks
            $livestockBlocks = $this->getFarmerLivestockBlocks($id);
            
            // Get farmer's crops data (plants and hectares)
            $cropsData = $this->getFarmerCropsData($id);
            
            // Get farmer's livestock data (animals count)
            $livestockData = $this->getFarmerLivestockData($id);
            
            // Get farmer's marketing data
            $marketingData = $this->getFarmerMarketingData($id);
            
            // Calculate totals
            $totals = [
                'total_hectares' => array_sum(array_column($cropsData, 'hectares')),
                'total_plants' => array_sum(array_column($cropsData, 'number_of_plants')),
                'total_male_animals' => array_sum(array_column($livestockData, 'he_total')),
                'total_female_animals' => array_sum(array_column($livestockData, 'she_total')),
                'total_animals' => array_sum(array_column($livestockData, 'he_total')) + array_sum(array_column($livestockData, 'she_total')),
                'total_marketing_value' => array_sum(array_map(function($item) {
                    return $item['quantity'] * $item['market_price_per_unit'];
                }, $marketingData))
            ];

            $data = [
                'title' => 'Farmer Profile - ' . $farmer['given_name'] . ' ' . $farmer['surname'],
                'page_header' => 'Farmer Profile',
                'menu' => 'admin-reports-farmers',
                'farmer' => $farmer,
                'cropBlocks' => $cropBlocks,
                'livestockBlocks' => $livestockBlocks,
                'cropsData' => $cropsData,
                'livestockData' => $livestockData,
                'marketingData' => $marketingData,
                'totals' => $totals
            ];

            return view('admin_farmers/admin_farmers_show', $data);

        } catch (\Exception $e) {
            log_message('error', 'AdminFarmers::show - ' . $e->getMessage());
            session()->setFlashdata('error', 'Error loading farmer profile: ' . $e->getMessage());
            return redirect()->to(base_url('admin/reports/farmers'));
        }
    }

    /**
     * Get farmers with location details and basic statistics
     */
    private function getFarmersWithLocationAndStats()
    {
        return $this->farmerModel
            ->select('farmer_information.*,
                     adx_province.name as province_name,
                     adx_district.name as district_name,
                     adx_llg.name as llg_name,
                     adx_ward.name as ward_name,
                     adx_country.name as country_name,
                     (SELECT COUNT(*) FROM crops_farm_blocks 
                      WHERE crops_farm_blocks.farmer_id = farmer_information.id 
                      AND crops_farm_blocks.status = "active") as crop_blocks_count,
                     (SELECT COUNT(*) FROM livestock_farm_blocks 
                      WHERE livestock_farm_blocks.farmer_id = farmer_information.id 
                      AND livestock_farm_blocks.status = "active") as livestock_blocks_count')
            ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
            ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
            ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
            ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
            ->join('adx_country', 'adx_country.id = farmer_information.country_id', 'left')
            ->where('farmer_information.deleted_at', null)
            ->orderBy('farmer_information.created_at', 'DESC')
            ->findAll();
    }

    /**
     * Get farmer with detailed location information
     */
    private function getFarmerWithLocationDetails($id)
    {
        return $this->farmerModel
            ->select('farmer_information.*,
                     adx_province.name as province_name,
                     adx_district.name as district_name,
                     adx_llg.name as llg_name,
                     adx_ward.name as ward_name,
                     adx_country.name as country_name')
            ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
            ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
            ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
            ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
            ->join('adx_country', 'adx_country.id = farmer_information.country_id', 'left')
            ->where('farmer_information.id', $id)
            ->where('farmer_information.deleted_at', null)
            ->first();
    }

    /**
     * Get farmer's crop farm blocks with crop details
     */
    private function getFarmerCropBlocks($farmerId)
    {
        return $this->cropsFarmBlockModel
            ->select('crops_farm_blocks.*,
                     adx_crops.crop_name,
                     adx_crops.crop_color_code')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id', 'left')
            ->where('crops_farm_blocks.farmer_id', $farmerId)
            ->where('crops_farm_blocks.status', 'active')
            ->findAll();
    }

    /**
     * Get farmer's livestock farm blocks
     */
    private function getFarmerLivestockBlocks($farmerId)
    {
        return $this->livestockFarmBlockModel
            ->where('farmer_id', $farmerId)
            ->where('status', 'active')
            ->findAll();
    }

    /**
     * Get farmer's crops data with crop details
     */
    private function getFarmerCropsData($farmerId)
    {
        return $this->cropsFarmCropsDataModel
            ->select('crops_farm_crops_data.*,
                     adx_crops.crop_name,
                     adx_crops.crop_color_code,
                     crops_farm_blocks.block_code')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_crops_data.crop_id', 'left')
            ->where('crops_farm_blocks.farmer_id', $farmerId)
            ->where('crops_farm_crops_data.status', 'active')
            ->findAll();
    }

    /**
     * Get farmer's livestock data with livestock details
     */
    private function getFarmerLivestockData($farmerId)
    {
        return $this->livestockFarmDataModel
            ->select('livestock_farm_data.*,
                     adx_livestock.name as livestock_name,
                     adx_livestock.color_code as livestock_color_code,
                     livestock_farm_blocks.block_code')
            ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
            ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id', 'left')
            ->where('livestock_farm_blocks.farmer_id', $farmerId)
            ->where('livestock_farm_data.status', 'active')
            ->findAll();
    }

    /**
     * Get farmer's marketing data with buyer details
     */
    private function getFarmerMarketingData($farmerId)
    {
        return $this->cropsFarmMarketingDataModel
            ->select('crops_farm_marketing_data.*,
                     adx_crops.crop_name,
                     crop_buyers.name as buyer_name')
            ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id', 'left')
            ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
            ->where('crops_farm_marketing_data.farmer_id', $farmerId)
            ->where('crops_farm_marketing_data.status', 'active')
            ->orderBy('crops_farm_marketing_data.market_date', 'DESC')
            ->findAll();
    }
}
